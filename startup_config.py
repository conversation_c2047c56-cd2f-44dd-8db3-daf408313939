from BASE.utils.path_selector import PathSelector
from logger.log import logger
from BASE.services.knowledge_bases import get_knowledge_bases_collection
import json
from pathlib import Path

@logger.catch()
def extract_kb_paths_from_database() -> list[str]:
    """Extract all paths from the knowledge bases database"""
    kb_paths = []
    kb_collection = get_knowledge_bases_collection()
    for kb in kb_collection.find():
        kb_paths.append(kb["metadata"]["path"])
    return kb_paths


@logger.catch()
def perform_startup_configuration() -> bool:
    codemate_folder = PathSelector.get_base_path()
    if not codemate_folder.exists():
        logger.error(f".codemate folder does not exist at {codemate_folder}")
        return False
    
    path_config_file = codemate_folder / "file_path.json"
    if path_config_file.exists():
        logger.error(f"File path config file already exist at {path_config_file}")
        return False
    
    kb_paths = extract_kb_paths_from_database()

    if kb_paths:
        config_data = {
            "monitored_paths": kb_paths
        }
        try:
            with open(path_config_file, "w") as f:
                json.dump(config_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error writing file path config file: {e}")
            return False
    return True 


@logger.catch()
def delete_kb_path(kb_path: str) -> bool:
    """Delete a path from monitored_paths in file_path.json"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "file_path.json"

    logger.info(f"Deleting path: {kb_path}")

    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return False

    if not config_path.exists():
        logger.error(f"Config file does not exist: {config_path}")
        return False

    try:
        with open(config_path, "r") as f:
            config = json.load(f)

        monitored_paths = config.get("monitored_paths")
        if not isinstance(monitored_paths, list):
            logger.error("Invalid structure: 'monitored_paths' should be a list.")
            return False


        for path in monitored_paths:
            if path == kb_path:
                monitored_paths.remove(path)
                break

        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)

    except (json.JSONDecodeError, OSError) as e:
        logger.error(f"Failed to update file: {e}")
        return False


def add_file_path(file_path: str) -> bool:
    """Add a path to monitored_paths in file_path.json"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "file_path.json"
    
    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return False
    
    if not config_path.exists():
        # make file 
        with open(config_path, "w") as f:
            json.dump({"monitored_paths": []}, f, indent=2)
    
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
            
        monitored_paths = config.get("monitored_paths")
        if not isinstance(monitored_paths, list):
            logger.error("Invalid structure: 'monitored_paths' should be a list.")
            return False
        
        
        for file in monitored_paths:
            if file == file_path:
                logger.warning(f"Path already exists in monitored_paths: {file_path}")
                return False
            
        
        monitored_paths.append(file_path)
        
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Added path: {file_path}")
        return True
    
    except (json.JSONDecodeError, OSError) as e:
        logger.error(f"Failed to update file: {e}")
        return False