from BASE.utils.path_selector import PathSelector
from logger.log import logger
from BASE.services.knowledge_bases import get_knowledge_bases_collection
import json

@logger.catch()
def extract_kb_paths_from_database() -> list[str]:
    """Extract all paths from the knowledge bases database"""
    kb_paths = []
    kb_collection = get_knowledge_bases_collection()
    for kb in kb_collection.find():
        kb_paths.append(kb["metadata"]["path"])
    return kb_paths


@logger.catch()
def perform_startup_configuration() -> bool:
    codemate_folder = PathSelector.get_base_path()
    if not codemate_folder.exists():
        logger.error(f".codemate folder does not exist at {codemate_folder}")
        return False
    
    path_config_file = codemate_folder / "file_path.json"
    if path_config_file.exists():
        logger.error(f"File path config file already exist at {path_config_file}")
        return False
    
    kb_paths = extract_kb_paths_from_database()

    if kb_paths:
        config_data = {
            "monitored_paths": kb_paths
        }
        try:
            with open(path_config_file, "w") as f:
                json.dump(config_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error writing file path config file: {e}")
            return False
    return True 